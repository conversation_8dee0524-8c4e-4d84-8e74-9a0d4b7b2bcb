<!-- 聊天主页 -->
<template>
  <div class="flex h-screen overflow-hidden bg-gray-50 font-sans">
    <!-- 左侧聊天列表 -->
    <ChatSidebar
      :contacts="chats"
      :current-contact-id="messageStore.currentChatUserId"
      :is-loading="isLoadingUsers"
      @select-contact="selectChat"
      @refresh="loadUsers"
    />

    <!-- 右侧聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <ChatHeader
        :current-contact="currentChat"
        @more-options="showMoreOptions"
        @logout="handleLogout"
      />

      <!-- 消息列表 -->
      <MessageList
        ref="messageListRef"
        :messages="currentMessages"
        :current-contact="currentChat"
      />

      <!-- 消息输入 -->
      <MessageInput
        :current-contact="currentChat"
        @send-message="sendMessage"
        @insert-emoji="insertEmoji"
        @attach-file="attachFile"
        @insert-image="insertImage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message'
import { apiClient } from '../api'
import type { User } from '../api'
import { WebSocketState } from '../services/websocketService'
import ChatSidebar from '../components/ChatSidebar.vue'
import ChatHeader from '../components/ChatHeader.vue'
import MessageList from '../components/MessageList.vue'
import MessageInput from '../components/MessageInput.vue'

// 状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

// 响应式数据
const isLoadingUsers = ref(false)
const messageListRef = ref<InstanceType<typeof MessageList>>()
const users = ref<User[]>([])

// 从API加载用户列表并预加载最后一条消息
const loadUsers = async () => {
  try {
    isLoadingUsers.value = true
    const response = await apiClient.getUsers()

    if (response.success) {
      users.value = response.users.filter((user) => user.id !== userStore.currentUser?.id)
      console.log('加载用户列表成功:', users.value)

      // 立即为每个用户加载最后一条消息（同步进行，确保渲染时有数据）
      await loadLastMessagesForUsers()
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 如果API失败，使用默认数据
    users.value = [
      {
        id: '689da0e4467567db78ae889d',
        username: 'chenhaowen',
        email: '<EMAIL>',
        displayName: '陈颢文',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: true,
        lastOnlineTime: '2025-08-14T08:42:27.539Z'
      },
      {
        id: '689da0e4467567db78ae889e',
        username: 'yangjing',
        email: '<EMAIL>',
        displayName: '杨敬',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: false,
        lastOnlineTime: '2025-08-14T08:39:39.183Z'
      }
    ]
    // 即使使用默认数据也要加载最后消息
    await loadLastMessagesForUsers()
  } finally {
    isLoadingUsers.value = false
  }
}

// 为所有用户加载最后一条消息
const loadLastMessagesForUsers = async () => {
  if (users.value.length === 0) return

  console.log('开始为用户列表加载最后一条消息...')

  // 并发为每个用户加载最后一条消息
  const loadPromises = users.value.map(async (user) => {
    try {
      // 只加载第一页的第一条消息（最新的）
      await messageStore.loadChatHistory(user.id, 1, 1)
      console.log(`加载用户 ${user.displayName} 的最后消息成功`)
    } catch (error) {
      console.error(`加载用户 ${user.displayName} 的最后消息失败:`, error)
    }
  })

  // 等待所有用户的最后消息加载完成
  await Promise.allSettled(loadPromises)
  console.log('所有用户的最后消息加载完成')
}

// 计算属性
const chats = computed(() => {
  // 创建一个Map来存储聊天会话信息
  const sessionMap = new Map()
  messageStore.sortedChatSessions.forEach((session) => {
    sessionMap.set(session.userId, session)
  })

  // 显示所有用户，包括没有聊天会话的用户
  return users.value.map((user) => {
    const session = sessionMap.get(user.id)

    if (session) {
      // 有聊天会话的用户
      return {
        id: user.id,
        name: user.displayName,
        avatar: getNameAvatar(user.displayName),
        status: user.isOnline ? '在线' : '离线',
        lastMessage: session.lastMessage?.content || '暂无消息',
        lastMessageTime: new Date(session.lastActiveTime),
        unreadCount: session.unreadCount,
        user: user
      }
    } else {
      // 没有聊天会话的用户
      return {
        id: user.id,
        name: user.displayName,
        avatar: getNameAvatar(user.displayName),
        status: user.isOnline ? '在线' : '离线',
        lastMessage: '暂无消息',
        lastMessageTime: new Date(), // 使用当前时间作为调用API时的时间
        unreadCount: 0,
        user: user
      }
    }
  })
})

const currentChat = computed(() => {
  if (!messageStore.currentChatUserId) return null
  return chats.value.find((chat) => chat.id === messageStore.currentChatUserId)
})

const currentMessages = computed(() => {
  return messageStore.currentMessages.map((msg) => ({
    id: msg.id,
    senderId: msg.senderId,
    senderName:
      msg.senderId === userStore.currentUser?.id
        ? userStore.userDisplayName || '我'
        : users.value.find((u) => u.id === msg.senderId)?.displayName || '未知用户',
    content: msg.content,
    timestamp: new Date(msg.timestamp),
    isSending: msg.isSending,
    sendError: msg.sendError
  }))
})

const wsConnectionStatus = computed(() => {
  switch (messageStore.wsState) {
    case WebSocketState.CONNECTED:
      return '已连接'
    case WebSocketState.CONNECTING:
      return '连接中...'
    case WebSocketState.RECONNECTING:
      return '重连中...'
    case WebSocketState.DISCONNECTED:
      return '已断开'
    case WebSocketState.ERROR:
      return '连接错误'
    default:
      return '未知状态'
  }
})

// 组件挂载时的初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isAuthenticated) {
    console.log('用户未登录，应该跳转到登录页')
    return
  }

  // 加载用户列表（包含最后一条消息）
  await loadUsers()

  // 初始化WebSocket连接
  try {
    await messageStore.initWebSocket(userStore.token)
    console.log('WebSocket连接初始化成功')
  } catch (error) {
    console.error('WebSocket连接初始化失败:', error)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  messageStore.disconnectWebSocket()
})

// 监听当前聊天用户变化，加载聊天历史
watch(
  () => messageStore.currentChatUserId,
  async (newUserId) => {
    if (newUserId) {
      try {
        await messageStore.loadChatHistory(newUserId)
        console.log(`加载用户${newUserId}的聊天历史`)
      } catch (error) {
        console.error('加载聊天历史失败:', error)
      }
    }
  }
)

// 工具函数
const getNameAvatar = (name: string) => {
  // 获取姓名的后两个字作为头像
  return name.length >= 2 ? name.slice(-2) : name
}

// 方法
const selectChat = (chatId: string) => {
  messageStore.setCurrentChatUser(chatId)
}

const showMoreOptions = () => {
  console.log('显示更多选项')
  console.log('WebSocket状态:', wsConnectionStatus.value)
  console.log('未读消息总数:', messageStore.totalUnreadCount)
  // TODO: 实现更多选项功能
}

const handleLogout = () => {
  messageStore.disconnectWebSocket()
  userStore.logout()
  // 触发父组件重新渲染，显示登录页面
  window.location.reload()
}

const insertEmoji = () => {
  console.log('插入表情')
  // TODO: 实现表情选择功能
}

const attachFile = () => {
  console.log('附加文件')
  // TODO: 实现文件上传功能
}

const insertImage = () => {
  console.log('插入图片')
  // TODO: 实现图片上传功能
}

const sendMessage = async (content: string) => {
  if (!content.trim() || !messageStore.currentChatUserId) {
    return
  }

  try {
    const success = await messageStore.sendMessage(messageStore.currentChatUserId, content)
    if (success) {
      console.log('消息发送成功')
    } else {
      console.error('消息发送失败')
    }
  } catch (error) {
    console.error('发送消息时出错:', error)
  }
}
</script>
