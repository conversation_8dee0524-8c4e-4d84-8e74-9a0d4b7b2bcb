<!-- 头像颜色演示组件 -->
<template>
  <div class="p-6 bg-white">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">头像颜色演示</h2>
    
    <!-- 预定义用户名演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-gray-700">预定义用户名</h3>
      <div class="grid grid-cols-3 md:grid-cols-6 gap-4">
        <div v-for="name in predefinedNames" :key="name" class="flex flex-col items-center">
          <UserAvatar :name="name" size="large" />
          <span class="text-sm text-gray-600 mt-2">{{ name }}</span>
        </div>
      </div>
    </div>

    <!-- 颜色一致性演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-gray-700">颜色一致性演示（同一用户多次显示）</h3>
      <div class="flex flex-wrap gap-4">
        <div v-for="i in 5" :key="i" class="flex flex-col items-center">
          <UserAvatar :name="consistencyTestName" size="medium" />
          <span class="text-xs text-gray-500 mt-1">{{ consistencyTestName }}</span>
        </div>
      </div>
    </div>

    <!-- 所有颜色展示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-gray-700">所有可用颜色</h3>
      <div class="flex flex-wrap gap-3">
        <div 
          v-for="(color, index) in availableColors" 
          :key="color"
          class="flex flex-col items-center"
        >
          <div 
            class="w-12 h-12 rounded-lg flex items-center justify-center text-white font-medium"
            :style="{ backgroundColor: color }"
          >
            {{ index + 1 }}
          </div>
          <span class="text-xs text-gray-500 mt-1">{{ color }}</span>
        </div>
      </div>
    </div>

    <!-- 自定义测试 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-gray-700">自定义测试</h3>
      <div class="flex items-center gap-4 mb-4">
        <input 
          v-model="customName"
          type="text" 
          placeholder="输入用户名..."
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button 
          @click="addCustomName"
          class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          添加
        </button>
        <button 
          @click="clearCustomNames"
          class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          清空
        </button>
      </div>
      <div class="grid grid-cols-3 md:grid-cols-6 gap-4">
        <div v-for="name in customNames" :key="name" class="flex flex-col items-center">
          <UserAvatar :name="name" size="large" />
          <span class="text-sm text-gray-600 mt-2">{{ name }}</span>
        </div>
      </div>
    </div>

    <!-- 随机颜色演示 -->
    <div>
      <h3 class="text-lg font-semibold mb-4 text-gray-700">随机颜色演示</h3>
      <div class="flex items-center gap-4 mb-4">
        <button 
          @click="generateRandomColors"
          class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          生成随机颜色
        </button>
      </div>
      <div class="flex flex-wrap gap-3">
        <div 
          v-for="(color, index) in randomColors" 
          :key="`random-${index}`"
          class="flex flex-col items-center"
        >
          <div 
            class="w-12 h-12 rounded-lg flex items-center justify-center text-white font-medium"
            :style="{ backgroundColor: color }"
          >
            R{{ index + 1 }}
          </div>
          <span class="text-xs text-gray-500 mt-1">{{ color }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UserAvatar from './UserAvatar.vue'
import { getAvatarColors, getRandomAvatarColor } from '../utils/avatarColors'

// 预定义的用户名
const predefinedNames = ref([
  '朱钰蒨',
  '陈颢文', 
  '杨敬',
  '张三',
  '李四',
  '王五',
  '赵六',
  '孙七',
  '周八',
  '吴九',
  '郑十',
  'Alice'
])

// 一致性测试用户名
const consistencyTestName = ref('朱钰蒨')

// 所有可用颜色
const availableColors = ref(getAvatarColors())

// 自定义用户名
const customName = ref('')
const customNames = ref<string[]>([])

// 随机颜色
const randomColors = ref<string[]>([])

// 添加自定义用户名
const addCustomName = () => {
  if (customName.value.trim() && !customNames.value.includes(customName.value.trim())) {
    customNames.value.push(customName.value.trim())
    customName.value = ''
  }
}

// 清空自定义用户名
const clearCustomNames = () => {
  customNames.value = []
}

// 生成随机颜色
const generateRandomColors = () => {
  randomColors.value = []
  for (let i = 0; i < 6; i++) {
    randomColors.value.push(getRandomAvatarColor())
  }
}

// 初始化随机颜色
generateRandomColors()
</script>
