# 调试指南

## 当前问题修复

### 1. 用户状态访问问题
**问题**: `userStore.currentUser.value` 为 undefined
**原因**: 在 Pinia store 中，响应式数据不需要使用 `.value` 访问
**修复**: 将所有 `userStore.xxx.value` 改为 `userStore.xxx`

### 2. Popover 组件错误
**问题**: `Cannot read properties of undefined (reading 'body')`
**原因**: `getPopupContainer` 函数参数类型和环境检查问题
**修复**: 
- 添加可选参数类型 `triggerNode?: HTMLElement`
- 添加环境检查 `typeof document === 'undefined'`

### 3. 登录后不跳转问题
**问题**: 登录成功后页面不跳转到聊天界面
**原因**: 响应式数据访问错误导致状态更新失效
**修复**: 修正所有用户状态的访问方式

## 调试步骤

### 1. 打开开发者工具
按 F12 或右键选择"检查"

### 2. 查看控制台日志
登录时应该看到以下日志：
```
初始化用户认证状态...
从localStorage获取的用户: null
从localStorage获取的token: null
用户未登录或token无效
开始登录请求: {username: "zhuyuqian"}
登录响应: {success: true, token: "...", user: {...}}
用户状态更新完成: {currentUser: {...}, token: "...", isAuthenticated: true}
登录成功: {...}
认证状态: true
当前认证状态: true
```

### 3. 检查状态更新
在控制台中运行：
```javascript
// 检查用户store状态
const userStore = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0].config.globalProperties.$pinia._s.get('user')
console.log('用户状态:', {
  currentUser: userStore.currentUser,
  isAuthenticated: userStore.isAuthenticated,
  token: userStore.token
})
```

### 4. 测试登录流程
1. 输入用户名: `zhuyuqian`
2. 输入密码: `123456`
3. 点击登录
4. 观察控制台日志
5. 确认页面跳转到聊天界面

## 常见问题排查

### 问题1: 登录后仍显示登录页面
**检查**: 
- 控制台是否有错误
- `userStore.isAuthenticated` 是否为 true
- App.vue 的 `currentView` 计算属性是否正确

**解决**: 
- 确保没有使用 `.value` 访问 Pinia store 数据
- 检查 localStorage 中是否正确保存了 token 和用户信息

### 问题2: WebSocket 连接失败
**检查**:
- 网络连接
- WebSocket 服务器地址配置
- 认证 token 是否正确

**解决**:
- 修改 `src/renderer/src/config/index.ts` 中的 `WS_URL`
- 设置 `USE_MOCK: true` 使用模拟数据

### 问题3: Popover 组件报错
**检查**:
- 是否在浏览器环境中运行
- `document` 对象是否可用

**解决**:
- 确保 `getPopupContainer` 函数有环境检查
- 使用可选参数避免 undefined 错误

## 开发建议

### 1. 使用 Vue DevTools
安装 Vue DevTools 浏览器扩展来调试 Vue 应用状态

### 2. 启用详细日志
在开发环境中保持详细的控制台日志

### 3. 分步测试
- 先测试登录功能
- 再测试 WebSocket 连接
- 最后测试消息收发

### 4. 模拟数据开发
在 `config/index.ts` 中设置 `USE_MOCK: true` 可以在没有后端服务的情况下开发和测试

## 下一步

如果问题仍然存在，请：
1. 提供完整的控制台错误信息
2. 确认是否按照上述步骤修复了代码
3. 检查是否有其他组件仍在使用 `.value` 访问 Pinia store
