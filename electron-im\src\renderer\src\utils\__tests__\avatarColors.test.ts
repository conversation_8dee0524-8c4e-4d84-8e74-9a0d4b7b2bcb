import { describe, it, expect } from 'vitest'
import { generateAvatarColor, getAvatarColors, getRandomAvatarColor } from '../avatarColors'

describe('avatarColors', () => {
  const expectedColors = [
    '#a4d46c', // 浅绿色
    '#7986cb', // 蓝紫色
    '#b0855e', // 棕色
    '#e08f70', // 橙色
    '#f06292', // 粉色
    '#4cb7ad'  // 青色
  ]

  describe('generateAvatarColor', () => {
    it('应该为相同的用户名生成相同的颜色', () => {
      const name = '朱钰蒨'
      const color1 = generateAvatarColor(name)
      const color2 = generateAvatarColor(name)
      expect(color1).toBe(color2)
    })

    it('应该为不同的用户名生成不同的颜色（大多数情况下）', () => {
      const names = ['朱钰蒨', '陈颢文', '杨敬', '张三', '李四']
      const colors = names.map(name => generateAvatarColor(name))
      
      // 检查是否有不同的颜色（不是所有颜色都相同）
      const uniqueColors = new Set(colors)
      expect(uniqueColors.size).toBeGreaterThan(1)
    })

    it('应该返回预定义颜色列表中的颜色', () => {
      const testNames = ['朱钰蒨', '陈颢文', '杨敬', '张三', '李四', '王五', '赵六']
      
      testNames.forEach(name => {
        const color = generateAvatarColor(name)
        expect(expectedColors).toContain(color)
      })
    })

    it('应该处理空字符串和空白字符串', () => {
      expect(generateAvatarColor('')).toBe(expectedColors[0])
      expect(generateAvatarColor('   ')).toBe(expectedColors[0])
    })

    it('应该处理单个字符的名字', () => {
      const color = generateAvatarColor('A')
      expect(expectedColors).toContain(color)
    })
  })

  describe('getAvatarColors', () => {
    it('应该返回所有预定义的颜色', () => {
      const colors = getAvatarColors()
      expect(colors).toEqual(expectedColors)
    })

    it('应该返回颜色数组的副本', () => {
      const colors1 = getAvatarColors()
      const colors2 = getAvatarColors()
      expect(colors1).not.toBe(colors2) // 不是同一个引用
      expect(colors1).toEqual(colors2) // 但内容相同
    })
  })

  describe('getRandomAvatarColor', () => {
    it('应该返回预定义颜色列表中的颜色', () => {
      for (let i = 0; i < 10; i++) {
        const color = getRandomAvatarColor()
        expect(expectedColors).toContain(color)
      }
    })

    it('应该能够返回不同的颜色（随机性测试）', () => {
      const colors = new Set()
      for (let i = 0; i < 50; i++) {
        colors.add(getRandomAvatarColor())
      }
      // 在50次调用中，应该至少有2种不同的颜色
      expect(colors.size).toBeGreaterThan(1)
    })
  })
})
