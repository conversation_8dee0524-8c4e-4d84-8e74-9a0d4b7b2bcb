// API 请求服务
import { API_CONFIG, APP_CONFIG } from '../config'

// 用户信息接口
export interface User {
  id: string
  username: string
  email: string
  displayName: string
  avatar: string
  department: string
  position: string
  isOnline: boolean
  lastOnlineTime: string
}

// 登录请求接口
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  token: string
  user: User
}

// 获取用户列表响应接口
export interface UsersResponse {
  success: boolean
  users: User[]
}

// 获取用户详情响应接口
export interface UserDetailResponse {
  success: boolean
  user: User
}

// 消息接口
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  type: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  pagination: {
    page: number
    limit: number
    pages: number
  }
}

// API 错误接口
export interface ApiError {
  success: false
  message: string
  code?: string
}

// HTTP 客户端类
class ApiClient {
  private baseURL: string
  private timeout: number

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
  }

  // 通用请求方法
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const token = localStorage.getItem(APP_CONFIG.TOKEN_KEY)

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers
      }
    }

    // 设置超时
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      clearTimeout(timeoutId)
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时')
        }
        if (error.message.includes('Failed to fetch')) {
          throw new Error('网络连接失败，请检查网络设置或服务器状态')
        }
        if (error.message.includes('Mixed Content')) {
          throw new Error('安全策略限制，请使用HTTPS连接')
        }
        throw error
      }
      throw new Error('网络请求失败')
    }
  }

  // 登录方法
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      let response: LoginResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟登录
        response = await this.mockLogin(credentials)
      } else {
        // 使用真实API
        response = await this.request<LoginResponse>(API_CONFIG.ENDPOINTS.LOGIN, {
          method: 'POST',
          body: JSON.stringify(credentials)
        })
      }

      if (response.success && response.token) {
        // 保存 token 和用户信息
        localStorage.setItem(APP_CONFIG.TOKEN_KEY, response.token)
        localStorage.setItem(APP_CONFIG.USER_KEY, JSON.stringify(response.user))
      }

      return response
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  }

  // 获取用户列表方法
  async getUsers(): Promise<UsersResponse> {
    try {
      let response: UsersResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        response = await this.mockGetUsers()
      } else {
        // 使用真实API
        response = await this.request<UsersResponse>(API_CONFIG.ENDPOINTS.USERS, {
          method: 'GET'
        })
      }

      return response
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  }

  // 获取用户详情方法
  async getUserDetail(userId: string): Promise<UserDetailResponse> {
    try {
      let response: UserDetailResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        response = await this.mockGetUserDetail(userId)
      } else {
        // 使用真实API
        response = await this.request<UserDetailResponse>(`/api/users/${userId}`, {
          method: 'GET'
        })
      }

      return response
    } catch (error) {
      console.error('获取用户详情失败:', error)
      throw error
    }
  }

  // 模拟登录方法 - 用于演示
  private async mockLogin(credentials: LoginRequest): Promise<LoginResponse> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 简单的用户名密码验证
    const validUsers = [
      {
        username: 'zhuyuqian',
        password: '123456',
        user: {
          id: '1',
          username: 'zhuyuqian',
          email: '<EMAIL>',
          displayName: '朱钰蒨',
          avatar: '👩',
          department: '产品部',
          position: '产品经理',
          isOnline: true,
          lastOnlineTime: new Date().toISOString()
        }
      }
    ]

    const validUser = validUsers.find(
      (user) => user.username === credentials.username && user.password === credentials.password
    )

    if (validUser) {
      return {
        success: true,
        token: `mock_token_${Date.now()}`,
        user: validUser.user
      }
    } else {
      throw new Error('用户名或密码错误')
    }
  }

  // 模拟获取用户列表方法
  private async mockGetUsers(): Promise<UsersResponse> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 模拟用户列表数据
    const users: User[] = [
      {
        id: '689da0e4467567db78ae889d',
        username: 'chenhaowen',
        email: '<EMAIL>',
        displayName: '陈颢文',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: true,
        lastOnlineTime: '2025-08-14T08:42:27.539Z'
      },
      {
        id: '689da0e4467567db78ae889e',
        username: 'yangjing',
        email: '<EMAIL>',
        displayName: '杨敬',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: false,
        lastOnlineTime: '2025-08-14T08:39:39.183Z'
      },
      {
        id: '1',
        username: 'zhuyuqian',
        email: '<EMAIL>',
        displayName: '朱钰蒨',
        avatar: '👩',
        department: '产品部',
        position: '产品经理',
        isOnline: true,
        lastOnlineTime: new Date().toISOString()
      }
    ]

    return {
      success: true,
      users
    }
  }

  // 模拟获取用户列表（包含最后消息）方法
  private async mockGetUsersWithLastMessage(): Promise<UsersWithLastMessageResponse> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300))

    const currentUser = this.getCurrentUser()

    // 模拟用户列表数据
    const users: User[] = [
      {
        id: '689da0e4467567db78ae889d',
        username: 'chenhaowen',
        email: '<EMAIL>',
        displayName: '陈颢文',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: true,
        lastOnlineTime: '2025-08-14T08:42:27.539Z'
      },
      {
        id: '689da0e4467567db78ae889e',
        username: 'yangjing',
        email: '<EMAIL>',
        displayName: '杨敬',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: false,
        lastOnlineTime: '2025-08-14T08:39:39.183Z'
      }
    ]

    // 模拟聊天历史数据
    const allMessages: Message[] = [
      {
        id: 'msg_1234567890',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889d',
        content: '你好陈颢文！',
        timestamp: 1755096147000,
        type: 1
      },
      {
        id: 'msg_1234567891',
        senderId: '689da0e4467567db78ae889d',
        receiverId: '1',
        content: '你好朱钰蒨！很高兴认识你',
        timestamp: 1755096150000,
        type: 1
      },
      {
        id: 'msg_1234567892',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889e',
        content: '杨敬，今天工作怎么样？',
        timestamp: 1755096160000,
        type: 1
      },
      {
        id: 'msg_1234567893',
        senderId: '689da0e4467567db78ae889e',
        receiverId: '1',
        content: '还不错，刚完成了一个功能模块',
        timestamp: 1755096170000,
        type: 1
      }
    ]

    // 过滤掉当前用户自己
    const filteredUsers = users.filter((user) => user.id !== currentUser?.id)

    // 为每个用户找到最后一条消息
    const usersWithLastMessage: UserWithLastMessage[] = filteredUsers.map((user) => {
      // 找到与该用户的所有消息
      const userMessages = allMessages.filter(
        (msg) =>
          (msg.senderId === currentUser?.id && msg.receiverId === user.id) ||
          (msg.senderId === user.id && msg.receiverId === currentUser?.id)
      )

      // 按时间排序，获取最后一条消息
      const sortedMessages = userMessages.sort((a, b) => b.timestamp - a.timestamp)
      const lastMessage = sortedMessages[0]

      return {
        user,
        lastMessage,
        lastMessageTime: lastMessage?.timestamp
      }
    })

    return {
      success: true,
      users: usersWithLastMessage
    }
  }

  // 模拟获取用户详情方法
  private async mockGetUserDetail(userId: string): Promise<UserDetailResponse> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300))

    // 模拟用户详情数据
    const users: User[] = [
      {
        id: '689da0e4467567db78ae889d',
        username: 'chenhaowen',
        email: '<EMAIL>',
        displayName: '陈颢文',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: true,
        lastOnlineTime: '2025-08-14T08:42:27.539Z'
      },
      {
        id: '689da0e4467567db78ae889e',
        username: 'yangjing',
        email: '<EMAIL>',
        displayName: '杨敬',
        avatar: '/avatars/default.png',
        department: '技术部',
        position: '前端工程师',
        isOnline: false,
        lastOnlineTime: '2025-08-14T08:39:39.183Z'
      },
      {
        id: '1',
        username: 'zhuyuqian',
        email: '<EMAIL>',
        displayName: '朱钰蒨',
        avatar: '👩',
        department: '产品部',
        position: '产品经理',
        isOnline: true,
        lastOnlineTime: new Date().toISOString()
      }
    ]

    // 根据 userId 查找用户（支持 ID 或用户名）
    const user = users.find((u) => u.id === userId || u.username === userId)

    if (user) {
      return {
        success: true,
        user
      }
    } else {
      throw new Error('用户不存在')
    }
  }

  // 登出方法
  logout(): void {
    localStorage.removeItem(APP_CONFIG.TOKEN_KEY)
    localStorage.removeItem(APP_CONFIG.USER_KEY)
  }

  // 获取当前用户信息
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(APP_CONFIG.USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }

  // 获取 token
  getToken(): string | null {
    return localStorage.getItem(APP_CONFIG.TOKEN_KEY)
  }

  // 获取聊天历史
  async getChatHistory(otherUserId: string, page = 1, limit = 50): Promise<ChatHistoryResponse> {
    try {
      let response: ChatHistoryResponse

      if (API_CONFIG.USE_MOCK) {
        // 使用模拟数据
        response = await this.mockGetChatHistory(otherUserId, page, limit)
      } else {
        // 使用真实API
        response = await this.request<ChatHistoryResponse>(
          `/api/messages/history/${otherUserId}?page=${page}&limit=${limit}`,
          { method: 'GET' }
        )
      }

      return response
    } catch (error) {
      console.error('获取聊天历史失败:', error)
      throw error
    }
  }

  // 模拟获取聊天历史方法
  private async mockGetChatHistory(
    otherUserId: string,
    page = 1,
    limit = 50
  ): Promise<ChatHistoryResponse> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 模拟聊天历史数据
    const allMessages: Message[] = [
      {
        id: 'msg_1234567890',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889d',
        content: '你好陈颢文！',
        timestamp: 1755096147000,
        type: 1
      },
      {
        id: 'msg_1234567891',
        senderId: '689da0e4467567db78ae889d',
        receiverId: '1',
        content: '你好朱钰蒨！很高兴认识你',
        timestamp: 1755096150000,
        type: 1
      },
      {
        id: 'msg_1234567892',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889d',
        content: '我也很高兴认识你，今天天气不错呢',
        timestamp: 1755096160000,
        type: 1
      },
      {
        id: 'msg_1234567893',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889e',
        content: '杨敬，今天工作怎么样？',
        timestamp: 1755096170000,
        type: 1
      },
      {
        id: 'msg_1234567894',
        senderId: '689da0e4467567db78ae889e',
        receiverId: '1',
        content: '还不错，刚完成了一个功能模块',
        timestamp: 1755096180000,
        type: 1
      },
      {
        id: 'msg_1234567895',
        senderId: '1',
        receiverId: '689da0e4467567db78ae889e',
        content: '很棒！有时间一起讨论技术问题',
        timestamp: 1755096190000,
        type: 1
      }
    ]

    // 根据用户ID过滤消息
    const currentUser = this.getCurrentUser()
    const filteredMessages = allMessages.filter(
      (msg) =>
        (msg.senderId === currentUser?.id && msg.receiverId === otherUserId) ||
        (msg.senderId === otherUserId && msg.receiverId === currentUser?.id)
    )

    // 分页处理
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedMessages = filteredMessages.slice(startIndex, endIndex)

    return {
      success: true,
      messages: paginatedMessages,
      pagination: {
        page,
        limit,
        pages: Math.ceil(filteredMessages.length / limit)
      }
    }
  }
}

// 导出 API 客户端实例
export const apiClient = new ApiClient()
